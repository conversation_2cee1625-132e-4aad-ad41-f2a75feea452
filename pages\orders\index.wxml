<wxs module="utils">
  function formatDate(dateStr) {
    if (!dateStr) return '';
    var date = getDate(dateStr);
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();
    var second = date.getSeconds();

    month = month < 10 ? '0' + month : month;
    day = day < 10 ? '0' + day : day;
    hour = hour < 10 ? '0' + hour : hour;
    minute = minute < 10 ? '0' + minute : minute;
    second = second < 10 ? '0' + second : second;

    return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
  }

  module.exports = {
    formatDate: formatDate
  };
</wxs>

<view class="container containermessage">
  <diy-navbar :isFixed="true" bgColor="white" CustomBar='60'>
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-start diygw-col-24 title-tab">
        <text
          wx:for="{{orderTypeTabs}}"
          wx:key="type"
          class="titleTab {{currentOrderType === item.type ? 'titleTabActive' : ''}}"
          bindtap="switchOrderType"
          data-type="{{item.type}}"
        >
          {{item.name}}
        </text>
      </view>
    </view>
  </diy-navbar>
  <view class="contentmessage">
    <!-- 订单状态切换标签 -->
    <view class="order-tabs">
      <view wx:for="{{orderTabs}}" wx:key="status" class="tab-item {{currentTab === item.status ? 'active' : ''}}" bindtap="switchTab" data-status="{{item.status}}">
        {{item.name}}
      </view>
    </view>
    <!-- 订单列表 -->
    <scroll-view scroll-y class="order-list" bindscrolltolower="loadMoreOrders">
      <block wx:if="{{orderList.length > 0}}">
        <view wx:for="{{orderList}}" wx:key="id" class="order-item">
          <!-- 可点击的卡片主体区域 -->
          <view class="order-card-clickable" data-item="{{item}}" bindtap="viewOrderDetail">
            <view class="order-header">
              <!-- 左侧：追加服务状态标签 -->
              <view class="header-left">
                <!-- 追加服务状态标签 -->
                <view wx:if="{{item.hasPendingAdditionalServices}}" class="additional-service-tags">
                  <text class="service-tag pending">追加服务待确认</text>
                </view>
              </view>
              <!-- 右侧：订单状态 -->
              <view class="header-right">
                <text class="order-status">{{item.status}}</text>
              </view>
            </view>
            <view class="order-content">
              <image class="product-image" src="{{item.productImage}}"></image>
              <view class="product-info">
                <view class="flex align-center justify-between">
                  <text class="product-name">{{item.productName}}</text>
                  <view class="price-container">
                    <!-- 总计价格 -->
                    <view class="total-price-section">
                      <text class="price-label">总计：</text>
                      <text class="total-amount">¥{{item.totalAmount}}</text>
                    </view>
                    <!-- 价格明细 -->
                    <view class="price-details">
                      <view class="price-detail-row">
                        <text wx:if="{{item.originalPrice && item.originalPrice > 0}}" class="original-price">原价：¥{{item.originalPrice}}</text>
                        <text class="actual-price">实付：¥{{item.totalFee || '0.00'}}</text>
                      </view>
                      <!-- 追加服务价格 -->
                      <view wx:if="{{item.hasAdditionalServices}}" class="price-detail-row additional-service-price">
                        <text wx:if="{{item.additionalServiceOriginalPrice && item.additionalServiceOriginalPrice > 0}}" class="original-price">追加原价：¥{{item.additionalServiceOriginalPrice}}</text>
                        <text class="actual-price">追加实付：¥{{item.additionalServiceAmount || '0.00'}}</text>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="flex align-center justify-between">
                  <text wx:if="{{item.extraServive.length > 0}}" class="product-service">增项服务：<text wx:for="{{item.extraServive}}" wx:for-item="val" wx:key="val">{{val}}{{index < item.extraServive.length-1 ? '、':''}}</text>
                  </text>
                  <text wx:else class="product-service">增项服务：无</text>
                </view>
              </view>
            </view>
            <view class="order-details">
              <view class="flex align-center justify-between magin-bottom">
                <text>订单号：</text>
                <text class="order-number-detail">{{item.sn}}</text>
              </view>
              <view class="flex align-center justify-between magin-bottom">
                <text>期待上门时间：</text>
                <text>{{item.expectTime}}</text>
              </view>
              <view class="flex align-center justify-between magin-bottom">
                <text>服务地址：</text>
                <text>{{item.addressDetail}}</text>
              </view>
              <view class="flex align-center justify-between magin-bottom">
                <text>创建时间：</text>
                <text>{{utils.formatDate(item.createdAt)}}</text>
              </view>
              <!-- 用户备注 -->
              <view class="flex align-center justify-between magin-bottom" wx:if="{{item.userRemark}}">
                <text>用户备注：</text>
                <text class="user-remark">{{item.userRemark}}</text>
              </view>
            </view>
          </view>

          <!-- 操作按钮区域（不可点击，避免冲突） -->
          <view class="order-actions-container" catchtap="preventTap">
            <view class="flex align-center justify-between">
              <view class='flex order-actions'>
                <!-- 根据订单状态显示不同的操作按钮 -->
                <block wx:if="{{item.status === '待服务'}}">
                  <!-- 主要操作按钮 -->
                  <view class="action-btn contact-btn" bindtap="contactCustomer" data-item="{{item}}">
                    联系客户
                  </view>
                  <view class="action-btn navigation-btn" bindtap="openNavigation" data-address="{{item.addressDetail}}" data-remark="{{item.addressRemark}}" data-latitude="{{item.latitude}}" data-longitude="{{item.longitude}}">
                    一键导航
                  </view>
                  <view class="action-btn primary-btn" bindtap="dispatch" data-item="{{item}}">
                    出发
                  </view>

                  <!-- 更多操作按钮 -->
                  <view class="more-actions-container">
                    <view class="action-btn more-btn" bindtap="toggleMoreActions" data-order-id="{{item.id}}" id="more-btn-{{item.id}}">
                      更多 {{activeMoreActionsOrderId === item.id ? '▲' : '▼'}}
                    </view>
                  </view>
                </block>

                <block wx:if="{{item.status === '已出发'}}">
                  <view class="action-btn blue-btn" bindtap="start" data-item="{{item}}">
                    开始服务
                  </view>
                </block>

                <block wx:if="{{item.status === '服务中'}}">
                  <view class="action-btn photo-btn" bindtap="uploadBeforePhotos" data-item="{{item}}">
                    上传服务前照片
                  </view>
                  <view class="action-btn special-note-btn" bindtap="showSpecialNote" data-item="{{item}}">
                    特殊情况说明
                  </view>
                  <view class="action-btn blue-btn" bindtap="complete" data-item="{{item}}">
                    结束服务
                  </view>
                </block>

                <!-- 已完成的订单显示上传服务后照片和查看评价按钮 -->
                <block wx:if="{{item.status === '已完成'}}">
                  <view class="action-btn photo-btn" bindtap="uploadAfterPhotos" data-item="{{item}}">
                    上传服务后照片
                  </view>
                  <view class="action-btn special-note-btn" bindtap="showSpecialNote" data-item="{{item}}">
                    查看特殊情况
                  </view>
                </block>

                <!-- 已完成且有评价的订单显示查看评价按钮 -->
                <block wx:if="{{item.status === '已评价'}}">
                  <view class="action-btn photo-btn" bindtap="uploadAfterPhotos" data-item="{{item}}">
                    上传服务后照片
                  </view>
                  <view class="action-btn special-note-btn" bindtap="showSpecialNote" data-item="{{item}}">
                    查看特殊情况
                  </view>
                  <view class="action-btn review-btn" bindtap="viewReview" data-item="{{item}}">
                    查看评价
                  </view>
                </block>
              </view>
            </view>
          </view>

          <!-- 更多操作弹窗 -->
          <view wx:if="{{item.showMoreActions}}" class="more-actions-dropdown">
            <view class="dropdown-item" bindtap="editServiceAddress" data-item="{{item}}">
              更改服务地址
            </view>
            <view class="dropdown-item" bindtap="deleteOrder" data-item="{{item}}">
              更换服务人员
            </view>
            <view class="dropdown-item" bindtap="toggleOrderActions" data-item="{{item}}">
              取消订单
            </view>
          </view>
        </view>
      </block>

      <view wx:else class="empty-list">
        <image src="//xian7.zos.ctyun.cn/pet/static/noreder.png" class="empty-image"></image>
        <text class="empty-text">暂无订单</text>
      </view>
    </scroll-view>
  </view>
  <!-- 时间选择器 -->
  <custom-picker
    wx:if="{{showTimePicker}}"
    bind:confirm="onTimeSelected"
    bind:cancel="onTimeCancel"
    selectedTime="{{selectedTime}}"
  />

  <!-- 服务照片上传组件 -->
  <service-photo-upload
    wx:if="{{showPhotoUpload}}"
    show="{{showPhotoUpload}}"
    photoType="{{currentPhotoType}}"
    orderInfo="{{currentOrderInfo}}"
    photoList="{{currentPhotoType === 'before' ? beforePhotos : afterPhotos}}"
    bind:photoChange="onPhotoChange"
    bind:confirm="onPhotoUploadConfirm"
    bind:cancel="onPhotoUploadCancel"
  />

  <!-- 特殊情况说明组件 -->
  <special-note-upload
    wx:if="{{showSpecialNote}}"
    show="{{showSpecialNote}}"
    orderInfo="{{currentOrderInfo}}"
    readonly="{{specialNoteReadonly}}"
    noteData="{{currentSpecialNoteData}}"
    bind:confirm="onSpecialNoteConfirm"
    bind:cancel="onSpecialNoteCancel"
    bind:delete="onSpecialNoteDelete"
  />

  <!-- 地址编辑器组件 -->
  <address-editor
    wx:if="{{showAddressEditor}}"
    show="{{showAddressEditor}}"
    orderInfo="{{currentEditOrder}}"
    bind:confirm="onAddressEditConfirm"
    bind:cancel="onAddressEditCancel"
  />

  <!-- 飘浮的下拉菜单 -->
  <view wx:if="{{activeMoreActionsOrderId}}" class="floating-dropdown-container">
    <!-- 遮罩层，点击关闭下拉菜单 -->
    <view class="order-dropdown-mask" bindtap="closeMoreActions"></view>
  </view>

  <!-- 下拉菜单（独立层级） -->
  <view wx:if="{{activeMoreActionsOrderId}}" class="floating-dropdown" style="{{dropdownStyle}}">
    <view class="order-dropdown-item" bindtap="reschedule" data-item="{{currentDropdownOrder}}" catchtap="reschedule">
      <text class="dropdown-icon">⏰</text>
      <text>更改时间</text>
    </view>
    <view class="order-dropdown-item" bindtap="editServiceAddress" data-item="{{currentDropdownOrder}}" catchtap="editServiceAddress">
      <text class="dropdown-icon">📍</text>
      <text>修改地址</text>
    </view>
  </view>

  <custom-tabbar currentActive='service' />
</view>