.container {
  background-color: #f1f1f1;
  min-height: 100vh;
}

/* .contentmessage {
  padding-bottom: 110px;
} */

.order-tabs {
  display: flex;
  background-color: #fff;
  height: 120rpx;
  align-items: center;
  border-radius: 0 0 32rpx 32rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  color: #666;
  font-size: 28rpx;
  position: relative;
  line-height: 70rpx;
}

.tab-item.active {
  color: #333;
  font-weight: bold;
  font-size: 30rpx;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 8rpx;
  border-radius: 8rpx;
  background-color: #FF4391;
}

.order-list {
  height: calc(100vh - 140px);
  padding-bottom: 110px;
}

.order-item {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
  position: relative;
}

/* 可点击的卡片主体区域 */
.order-card-clickable {
  padding: 20rpx;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
}

/* 添加微妙的悬停效果提示（在支持的设备上） */
.order-card-clickable:hover {
  background-color: #fafafa;
}

/* 点击反馈效果 */
.order-card-clickable:active {
  background-color: #f0f0f0;
  transform: scale(0.98);
}

/* 操作按钮容器 */
.order-actions-container {
  padding: 16rpx 20rpx 20rpx 20rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
  position: relative;
  z-index: 2;
}

/* 确保按钮区域不会被点击事件影响 */
.order-actions-container .action-btn {
  position: relative;
  z-index: 3;
}

/* 地址文本样式 */
.address-text {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-item .flex {
  width: 100%;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  min-height: 40rpx;
}

.header-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 订单详情中的订单号样式 */
.order-number-detail {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
}

/* 追加服务状态标签 */
.additional-service-tags {
  display: flex;
  gap: 8rpx;
}

.service-tag {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  white-space: nowrap;
  font-weight: 500;
}

.service-tag.pending {
  background: #ff9500;
  color: #fff;
}

.service-tag.processing {
  background: #1890ff;
  color: #fff;
}

.service-tag.completed {
  background: #52c41a;
  color: #fff;
}

.header-right {
  display: flex;
  align-items: flex-end;
}

.order-status {
  font-size: 28rpx;
  color: #FF4391;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 订单详情区域 */
.order-details {
  margin-bottom: 0;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 150rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(47, 131, 255, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 100%;
  line-height: 50rpx;
  margin-top: 16rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

/* 价格容器 */
.price-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

/* 总计价格区域 */
.total-price-section {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.price-label {
  font-size: 24rpx;
  color: #666;
}

.total-amount {
  color: #FF4391;
  font-size: 32rpx;
  font-weight: bold;
}

/* 价格明细区域 */
.price-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.price-detail-row {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

/* 原价样式 */
.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
}

.actual-price {
  color: #666;
  font-size: 22rpx;
}

/* 追加服务价格样式 */
.additional-service-price {
  padding-top: 4rpx;
  border-top: 1rpx solid #f0f0f0;
}

.additional-service-price .original-price,
.additional-service-price .actual-price {
  font-size: 20rpx;
  color: #1890ff;
}

/* 兼容原有样式 */
.total-fee {
  font-size: 32rpx;
  color: #ff4391;
  font-weight: bold;
}

.magin-bottom {
  margin-bottom: 20rpx;
  align-items: baseline;
}

.magin-bottom text:nth-child(1) {
  width: 260rpx;
}

.more-btn {
  margin-right: 20rpx;
  color: rgba(47, 131, 255, 1);
  font-size: 24rpx;
  position: relative;
}

.more-actions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 260rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
}

.dropdown-item {
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.3s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f4f4f4;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.action-btn {
  padding: 10rpx 20rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
  margin-left: 20rpx;
  white-space: nowrap;
}

/* 联系客户按钮 */
.action-btn.contact-btn {
  background: rgba(255, 107, 157, 1);
  color: white;
}

/* 导航按钮 */
.action-btn.navigation-btn {
  background: rgba(76, 175, 80, 1);
  color: white;
}

/* 主要操作按钮（出发） */
.action-btn.primary-btn {
  background: rgba(47, 131, 255, 1);
  color: white;
}

/* 更多操作容器 */
.more-actions-container {
  position: relative;
  margin-left: 20rpx;
}

.action-btn.more-btn {
  background: rgba(158, 158, 158, 1);
  color: white;
  font-size: 22rpx;
  padding: 10rpx 16rpx;
}

/* 飘浮下拉菜单容器 */
.floating-dropdown-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;
}

.floating-dropdown-container .order-dropdown-mask {
  pointer-events: auto;
}

/* 飘浮下拉菜单样式 */
.floating-dropdown {
  position: fixed;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  min-width: 200rpx;
  animation: dropdownFadeIn 0.3s ease;
  z-index: 10000;
  pointer-events: auto;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(10rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.order-dropdown-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 24rpx;
  font-size: 26rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-dropdown-item:last-child {
  border-bottom: none;
}

.order-dropdown-item:active {
  background-color: #f8f9fa;
}

.dropdown-icon {
  font-size: 28rpx;
  width: 28rpx;
  text-align: center;
}

/* 下拉菜单遮罩层 */
.order-dropdown-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.review-btn {
  background: rgba(255, 165, 0, 1) !important;
  color: white !important;
}

.photo-btn {
  background: rgba(255, 67, 145, 0.1) !important;
  color: rgba(255, 67, 145, 1) !important;
  border: 1rpx solid rgba(255, 67, 145, 0.3) !important;
}

.special-note-btn {
  background: rgba(255, 152, 0, 0.1) !important;
  color: rgba(255, 152, 0, 1) !important;
  border: 1rpx solid rgba(255, 152, 0, 0.3) !important;
}

.navigation-btn {
  background: rgba(34, 197, 94, 0.1) !important;
  color: rgba(34, 197, 94, 1) !important;
  border: 1rpx solid rgba(34, 197, 94, 0.3) !important;
}

.contact-btn {
  background: rgba(59, 130, 246, 0.1) !important;
  color: rgba(59, 130, 246, 1) !important;
  border: 1rpx solid rgba(59, 130, 246, 0.3) !important;
}

.empty-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 200rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
}

.empty-text {
  margin-top: 20rpx;
  color: #999;
}

.titleTab {
  margin-right: 25rpx;
  font-size: 30rpx;
}

.titleTab:first-child {
  margin-left: 35rpx;
}

.titleTabActive {
  font-weight: bold;
  font-size: 36rpx;
  color: #3083FF;
}

/* 用户备注样式 */
.user-remark {
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
  color: #666;
  font-size: 26rpx;
  max-width: 300rpx;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}