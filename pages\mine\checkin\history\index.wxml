<view class="container">
  <!-- 筛选区域 -->
  <view class="filter-section">
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">🔍</view>
        <input
          class="search-input"
          placeholder="搜索打卡描述..."
          value="{{keyword}}"
          bind:input="onKeywordInput"
          bind:confirm="onSearch"
        />
        <view wx:if="{{keyword}}" class="clear-icon" bind:tap="clearKeyword">✕</view>
      </view>
      <view class="search-btn" bind:tap="onSearch">搜索</view>
    </view>

    <!-- 日期筛选 -->
    <view class="date-filter-container">
      <view class="date-filter-row">
        <view class="date-picker-item">
          <text class="date-label">开始日期</text>
          <view
            class="date-picker {{startDate ? 'has-value' : ''}}"
            data-type="start"
            bind:tap="showDatePicker"
          >
            <text class="date-text">{{startDate || '选择日期'}}</text>
            <text class="date-icon">📅</text>
          </view>
        </view>

        <view class="date-picker-item">
          <text class="date-label">结束日期</text>
          <view
            class="date-picker {{endDate ? 'has-value' : ''}}"
            data-type="end"
            bind:tap="showDatePicker"
          >
            <text class="date-text">{{endDate || '选择日期'}}</text>
            <text class="date-icon">📅</text>
          </view>
        </view>
      </view>

      <view wx:if="{{startDate || endDate || keyword}}" class="filter-actions">
        <view class="clear-filter-btn" bind:tap="clearFilters">
          <text class="clear-icon">🗑️</text>
          <text>清除筛选</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 记录列表 -->
  <view class="list-section">
    <view wx:if="{{checkinList.length === 0 && !loading}}" class="empty-state">
      <text class="empty-text">暂无更多记录了</text>
    </view>

    <view wx:else class="checkin-list">
      <view
        wx:for="{{checkinList}}"
        wx:key="id"
        class="checkin-card"
      >
        <!-- 卡片头部 -->
        <view class="card-header">
          <view class="time-info">
            <text class="time-text">{{item.formatTime}}</text>
          </view>
          <button
            class="delete-btn"
            data-id="{{item.id}}"
            data-index="{{index}}"
            bind:tap="deleteCheckin"
          >删除</button>
        </view>

        <!-- 卡片内容 -->
        <view class="card-content">
          <!-- 描述信息 -->
          <view class="info-row">
            <text class="info-label">描述:</text>
            <text class="info-value">{{item.description}}</text>
          </view>

          <!-- 位置信息 -->
          <view wx:if="{{item.displayAddress}}" class="info-row">
            <text class="info-label">位置:</text>
            <text class="info-value location">{{item.displayAddress}}</text>
          </view>

          <!-- 照片信息统计 -->
          <view wx:if="{{item.photos}}" class="info-row">
            <text class="info-label">照片:</text>
            <text class="info-value">
              <text wx:if="{{item.photos.vehicleExterior && item.photos.vehicleExterior.length > 0}}">车辆外观{{item.photos.vehicleExterior.length}}张 </text>
              <text wx:if="{{item.photos.serviceStaff && item.photos.serviceStaff.length > 0}}">服务人员{{item.photos.serviceStaff.length}}张 </text>
              <text wx:if="{{item.photos.vehicleInterior && item.photos.vehicleInterior.length > 0}}">车内情况{{item.photos.vehicleInterior.length}}张</text>
            </text>
          </view>

          <!-- 分组照片展示 -->
          <view wx:if="{{item.photos}}" class="photos-section">
            <!-- 车辆外观照片 -->
            <view wx:if="{{item.photos.vehicleExterior && item.photos.vehicleExterior.length > 0}}" class="photo-group">
              <text class="group-title">车辆外观 ({{item.photos.vehicleExterior.length}}张)</text>
              <view class="photos-grid">
                <image
                  wx:for="{{item.photos.vehicleExterior}}"
                  wx:for-item="photo"
                  wx:key="index"
                  src="{{photo}}"
                  class="photo-item"
                  mode="aspectFill"
                  data-url="{{photo}}"
                  data-photos="{{item.photos}}"
                  data-type="vehicleExterior"
                  bind:tap="previewImage"
                ></image>
              </view>
            </view>

            <!-- 服务人员照片 -->
            <view wx:if="{{item.photos.serviceStaff && item.photos.serviceStaff.length > 0}}" class="photo-group">
              <text class="group-title">服务人员 ({{item.photos.serviceStaff.length}}张)</text>
              <view class="photos-grid">
                <image
                  wx:for="{{item.photos.serviceStaff}}"
                  wx:for-item="photo"
                  wx:key="index"
                  src="{{photo}}"
                  class="photo-item"
                  mode="aspectFill"
                  data-url="{{photo}}"
                  data-photos="{{item.photos}}"
                  data-type="serviceStaff"
                  bind:tap="previewImage"
                ></image>
              </view>
            </view>

            <!-- 车内情况照片 -->
            <view wx:if="{{item.photos.vehicleInterior && item.photos.vehicleInterior.length > 0}}" class="photo-group">
              <text class="group-title">车内情况 ({{item.photos.vehicleInterior.length}}张)</text>
              <view class="photos-grid">
                <image
                  wx:for="{{item.photos.vehicleInterior}}"
                  wx:for-item="photo"
                  wx:key="index"
                  src="{{photo}}"
                  class="photo-item"
                  mode="aspectFill"
                  data-url="{{photo}}"
                  data-photos="{{item.photos}}"
                  data-type="vehicleInterior"
                  bind:tap="previewImage"
                ></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <text>加载中...</text>
    </view>

    <view wx:if="{{!hasMore && checkinList.length > 0}}" class="no-more">
      <text>暂无更多记录了</text>
    </view>
  </view>

  <!-- 日期选择器 -->
  <picker
    wx:if="{{showDatePicker}}"
    mode="date"
    value="{{datePickerType === 'start' ? startDate : endDate}}"
    bind:change="onDateChange"
    bind:cancel="onDateCancel"
  >
    <view></view>
  </picker>
</view>
